# Authentication & User Management Flow

## Overview
The Worthy Freshers platform implements a comprehensive authentication system with admin approval workflows, role-based access control, and multi-environment support.

## Authentication Architecture

### Core Components
```
┌──────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend       │    │   Firebase      │    │   Firestore     │
│   Components     │    │   Services      │    │   Collections   │
├──────────────────┤    ├─────────────────┤    ├─────────────────┤
│ • Login          │◄──►│ • Auth          │◄──►│ • pendingRegs   │
│ • Register       │    │ • Functions     │    │ • users         │
│ • PendingApproval│    │ • Custom Claims │    │ • auditLogs     │
│ • AdminDashboard │    │ • Security Rules│    │ • systemConfig  │
└──────────────────┘    └─────────────────┘    └─────────────────┘
```

## User Registration Flow

### Step 1: User Registration
```mermaid
sequenceDiagram
    participant U as User
    participant F as Frontend
    participant FA as Firebase Auth
    participant FF as Firebase Functions
    participant FS as Firestore

    U->>F: Access /auth/register
    U->>F: Fill registration form
    F->>FA: createUserWithEmailAndPassword()
    FA-->>F: User created (uid)
    F->>FA: updateProfile(displayName)
    F->>FF: createPendingRegistration()
    FF->>FS: Store in pendingRegistrations
    FF->>FA: Set custom claims (status: 'pending')
    FF-->>F: Registration successful
    F->>F: Redirect to /auth/pending-approval
```

### Step 2: Pending Approval State
```mermaid
sequenceDiagram
    participant U as User
    participant F as Frontend
    participant PS as PendingRegistrationService
    participant FS as Firestore

    U->>F: Access /auth/pending-approval
    F->>PS: checkRegistrationStatus()
    PS->>FS: Query pendingRegistrations/{uid}
    FS-->>PS: Registration data
    PS-->>F: Status response
    F->>F: Display status (pending/approved/rejected)
    
    Note over F: Auto-refresh every 30 seconds
    F->>PS: refreshStatus() (interval)
    
    alt Status = approved
        F->>F: Redirect to /dashboard
    else Status = rejected
        F->>F: Show rejection reason
    else Status = pending
        F->>F: Show waiting message
    end
```

### Step 3: Admin Approval Process
```mermaid
sequenceDiagram
    participant A as Admin
    participant AD as AdminDashboard
    participant PS as PendingRegistrationService
    participant FF as Firebase Functions
    participant FA as Firebase Auth
    participant FS as Firestore

    A->>AD: Access admin dashboard
    AD->>PS: getPendingRegistrations()
    PS->>FS: Real-time listener on pendingRegistrations
    FS-->>AD: Live pending registrations list
    
    A->>AD: Click "Approve" on registration
    AD->>PS: approvePendingRegistration(uid)
    PS->>FF: approvePendingRegistrationHttp()
    FF->>FS: Update registration status to 'approved'
    FF->>FA: Set custom claims (role, status: 'approved')
    FF-->>PS: Approval successful
    PS-->>AD: Update UI
    
    Note over FS: Real-time update triggers
    FS-->>AD: Updated registration list
```

## Authentication Guards

### Guard Hierarchy
```
ApprovedUserGuard (Main App Protection)
├── Checks user authentication
├── Verifies approval status
├── Redirects based on status
└── Allows access to approved users

RoleGuard (Feature Protection)
├── Extends ApprovedUserGuard
├── Checks specific role requirements
├── Validates role permissions
└── Redirects unauthorized users

RedirectApprovedGuard (Auth Page Protection)
├── Prevents approved users from auth pages
├── Redirects to dashboard if approved
└── Allows access for unapproved users
```

### Guard Implementation Details

#### ApprovedUserGuard
```typescript
// Route Protection Logic
async canActivate(): Promise<boolean> {
  const user = auth.currentUser;
  if (!user) {
    router.navigate(['/auth/login']);
    return false;
  }

  const claims = await getCurrentUserClaims();
  
  if (claims.status === 'approved' && claims.role) {
    return true; // Allow access
  } else if (claims.status === 'pending') {
    router.navigate(['/auth/pending-approval']);
    return false;
  } else if (claims.status === 'rejected') {
    router.navigate(['/auth/pending-approval']);
    return false;
  }
  
  router.navigate(['/auth/login']);
  return false;
}
```

#### RoleGuard
```typescript
// Role-based Access Control
async canActivate(route): Promise<boolean> {
  const requiredRoles = route.data?.roles || [];
  const claims = await getCurrentUserClaims();
  
  if (claims.status !== 'approved') {
    router.navigate(['/auth/pending-approval']);
    return false;
  }
  
  if (requiredRoles.includes(claims.role)) {
    return true;
  }
  
  router.navigate(['/dashboard']);
  return false;
}
```

## User Roles & Permissions

### Role Definitions
```typescript
type UserRole = 'admin' | 'student' | 'trainer' | 'parent' | 'college-staff';

interface UserClaims {
  role: UserRole;
  status: 'pending' | 'approved' | 'rejected';
  approvedAt?: number;
  approvedBy?: string;
  registeredAt: number;
}
```

### Permission Matrix
| Route | Admin | College Staff | Trainer | Student | Parent |
|-------|-------|---------------|---------|---------|--------|
| `/dashboard` | ✅ | ✅ | ✅ | ✅ | ✅ |
| `/registrations/institute` | ✅ | ✅ | ❌ | ❌ | ❌ |
| `/registrations/student` | ✅ | ✅ | ✅ | ❌ | ❌ |
| `/registrations/trainer` | ✅ | ✅ | ❌ | ❌ | ❌ |
| `/registrations/parent` | ✅ | ✅ | ❌ | ❌ | ❌ |
| `/registrations/employee` | ✅ | ❌ | ❌ | ❌ | ❌ |
| `/system/settings` | ✅ | ❌ | ❌ | ❌ | ❌ |

## Database Schema

### pendingRegistrations Collection
```typescript
interface PendingRegistration {
  uid: string;                    // Firebase Auth UID
  email: string;                  // User email
  name: string;                   // Display name
  role: UserRole;                 // Requested role
  status: 'pending' | 'approved' | 'rejected';
  submittedAt: Timestamp;         // Registration timestamp
  verificationData?: {            // Role-specific verification
    studentId?: string;
    institutionEmail?: string;
    credentials?: string[];
    parentStudentLink?: string;
    // ... other verification fields
  };
  approvedBy?: string;            // Admin UID who approved
  approvedAt?: Timestamp;         // Approval timestamp
  rejectedBy?: string;            // Admin UID who rejected
  rejectedAt?: Timestamp;         // Rejection timestamp
  rejectionReason?: string;       // Reason for rejection
}
```

### users Collection (Post-Approval)
```typescript
interface User {
  uid: string;
  email: string;
  name: string;
  role: UserRole;
  status: 'approved';
  createdAt: Timestamp;
  lastLoginAt?: Timestamp;
  profile: {
    // Role-specific profile data
  };
}
```

## Security Implementation

### Firestore Security Rules
```javascript
// Pending Registrations Access
match /pendingRegistrations/{uid} {
  // Users can read their own registration
  allow read: if request.auth != null && request.auth.uid == uid;
  
  // Only admins can read all registrations
  allow read: if isAdmin();
  
  // Only admins can update registrations (approve/reject)
  allow write: if isAdmin();
}

// Helper Functions
function isAuthenticated() {
  return request.auth != null;
}

function isApproved() {
  return isAuthenticated() && 
         request.auth.token.status == 'approved';
}

function isAdmin() {
  return isAuthenticated() && 
         request.auth.token.role == 'admin' && 
         request.auth.token.status == 'approved';
}
```

### Firebase Functions Security
```typescript
// Admin-only function example
export const approvePendingRegistration = functions.https.onCall(
  async (request, context) => {
    // Verify authentication
    if (!context.auth) {
      throw new functions.https.HttpsError(
        'unauthenticated',
        'User must be authenticated'
      );
    }

    // Verify admin role
    const adminToken = await admin.auth().getUser(context.auth.uid);
    const adminClaims = adminToken.customClaims;

    if (!adminClaims?.role || adminClaims.role !== 'admin') {
      throw new functions.https.HttpsError(
        'permission-denied',
        'Only admins can approve registrations'
      );
    }

    // Process approval...
  }
);
```

## Error Handling & Edge Cases

### Common Scenarios
1. **Network Failures**: Retry mechanisms with exponential backoff
2. **Token Expiration**: Automatic token refresh
3. **Concurrent Approvals**: Optimistic locking with conflict resolution
4. **Invalid States**: State validation and correction
5. **Memory Leaks**: Proper cleanup of intervals and listeners

### Error Recovery
```typescript
// Service-level error handling
async createPendingRegistration(userData) {
  try {
    // Try onCall function first
    const result = await this.createPendingRegistrationOnCall(userData);
    return result;
  } catch (error) {
    console.warn('onCall failed, trying HTTP fallback:', error);
    
    try {
      // Fallback to HTTP function
      return await this.createPendingRegistrationHttp(userData);
    } catch (httpError) {
      console.error('Both methods failed:', { error, httpError });
      throw error; // Throw original error
    }
  }
}
```

## Performance Optimizations

### Frontend Optimizations
- **Lazy Loading**: All auth routes are lazy-loaded
- **Signal-based State**: Reactive state management with Angular Signals
- **Memory Management**: Proper cleanup of intervals and event listeners
- **Caching**: User claims cached to reduce API calls

### Backend Optimizations
- **Real-time Listeners**: Efficient Firestore real-time updates
- **Indexed Queries**: Optimized database queries with proper indexing
- **Function Optimization**: Minimal cold start times
- **CORS Handling**: Dual function approach (onCall + HTTP)

## Testing Strategy

### Unit Tests
- Guard logic testing
- Service method testing
- Component behavior testing
- Error handling validation

### Integration Tests
- End-to-end registration flow
- Admin approval workflow
- Role-based access testing
- Security rule validation

### Performance Tests
- Load testing for concurrent registrations
- Database query performance
- Function execution time monitoring
- Memory leak detection

---

*This document provides comprehensive details about the authentication and user management system in Worthy Freshers.*
